package com.metacards.metacards

import android.app.Application
import android.content.Context
import co.touchlab.kermit.Logger
import co.touchlab.kermit.Severity
import com.google.firebase.FirebaseApp
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.KoinProvider
import com.metacards.metacards.core.notification.createNotificationChannels
import com.metacards.metacards.core.utils.isDebugBuild
import io.branch.referral.Branch
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import org.koin.core.Koin

class App : Application(), KoinProvider {

    // TODO: add scopes to koin after refactor main flow
    private val appCoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate)

    override lateinit var koin: Koin
        private set

    override fun onCreate() {
        super.onCreate()
        initLogger()
        FirebaseApp.initializeApp(this)
        koin = createKoin()
        createNotificationChannels(this)
        initBranchIO()
    }

    private fun initLogger() {
        if (!isDebugBuild) {
            Logger.setMinSeverity(Severity.Assert)
        }
    }

    private fun createKoin(): Koin {
        return Koin().apply {
            loadModules(allModules)
            declare(this@App as Application)
            declare(this@App as Context)
            declare(appCoroutineScope)
            declare(ComponentFactory(this))
            createEagerInstances()
        }
    }

    private fun initBranchIO() {
        Branch.enableLogging()
        Branch.getAutoInstance(this)
    }
}
