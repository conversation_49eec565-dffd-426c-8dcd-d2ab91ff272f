package com.metacards.metacards

import com.metacards.metacards.core.coreModule
import com.metacards.metacards.features.account.accountModule
import com.metacards.metacards.features.advbanner.advBannerModule
import com.metacards.metacards.features.analytics.analyticsModule
import com.metacards.metacards.features.auth.authModule
import com.metacards.metacards.features.course.courseModule
import com.metacards.metacards.features.deck.deckModule
import com.metacards.metacards.features.deeplink.deeplinkModule
import com.metacards.metacards.features.favorite_cards.favoriteCardsModule
import com.metacards.metacards.features.layout.layoutModule
import com.metacards.metacards.features.main.mainModule
import com.metacards.metacards.features.payments.paymentModule
import com.metacards.metacards.features.record.recordModule
import com.metacards.metacards.features.root.rootModule
import com.metacards.metacards.features.special_deck.specialDeckModule
import com.metacards.metacards.features.splash.splashModule
import com.metacards.metacards.features.tutorial.tutorialModule
import com.metacards.metacards.features.user.userModule
import com.metacards.metacards.features.user_analytics.userAnalyticsModule

val allModules = listOf(
    coreModule(),
    rootModule,
    authModule,
    mainModule,
    advBannerModule,
    deckModule,
    userModule,
    splashModule,
    recordModule,
    layoutModule,
    accountModule,
    favoriteCardsModule,
    userAnalyticsModule,
    deeplinkModule,
    analyticsModule,
    tutorialModule,
    paymentModule,
    specialDeckModule,
    courseModule
)