<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="special_deck_details_button_great">EXCELLENT</string>
    <string name="special_deck_details_title_assemble_new_deck">Collect new deck</string>
    <string name="special_deck_details_button_rotate">Tap to flip</string>
    <string name="special_deck_details_text_scratch">Rub the screen</string>
    <string name="special_deck_details_text_break_glass">Tap the screen</string>
    <string name="special_deck_details_title_card_opened">Open card</string>
    <string name="special_deck_details_title_card_new">New card</string>

    <string name="special_deck_close_dialog_title">Sure about exiting?</string>
    <string name="special_deck_close_dialog_text">The card won\'t be received, and you\'ll need to repeat the process the next time you log in</string>
    <string name="special_deck_close_dialog_btn_confirm">Exit</string>
    <string name="special_deck_close_dialog_btn_dismiss">Cancel</string>

    <string name="special_deck_first_card_popup_title">Your first card has been received</string>
    <string name="special_deck_first_card_popup_text">Keep up the good work, a new card will be available tomorrow</string>
    <string name="special_deck_first_card_popup_btn_confirm">Okay</string>

    <string name="special_deck_onboarding_description_1">Hi</string>
    <string name="special_deck_onboarding_description_2">Collect the \"%s\" deck</string>
    <string name="special_deck_onboarding_steps_block_text_1">Discover two new stars each day</string>
    <string name="special_deck_onboarding_steps_block_text_2">Receive cards from the \"%s\" deck</string>
    <string name="special_deck_onboarding_steps_block_text_3">Collect asterisms to get a deck. You can use the deck for prompts once you\'ve collected all the cards.</string>
    <string name="special_deck_onboarding_button_start">Start</string>

    <string name="special_deck_constellation_obtained_button_wow">Okay</string>

    <string name="special_deck_deck_obtained_title">Deck added</string>
    <string name="special_deck_deck_obtained_subtitle">Congratulations! All the cards are collected.</string>
    <string name="special_deck_deck_obtained_subtitle_2">The deck has been added to the app, you can start the prompt</string>
    <string name="special_deck_deck_obtained_button_go">My universe</string>


    <string name="special_deck_card_opening_cooldown_title">The next star will be available in %1$dh %2$dm!</string>
    <string name="special_deck_card_opening_cooldown_description_1">Your card has been discovered. Please come back in %1$dh %2$dm for a new one</string>
    <string name="special_deck_card_opening_cooldown_description_2">You can view already revealed cards by tapping on the discovered stars</string>
    <string name="special_deck_card_opening_cooldown_button_ok">Okay</string>

    <string name="special_deck_swipe_tutorial_title">This is your universe</string>
    <string name="special_deck_swipe_tutorial_description">Swipe right or left to choose the star of the day</string>
    <string name="special_deck_swipe_tutorial_button_wow">Okay</string>

    <string name="special_deck_starry_sky_title">Collect new deck</string>
    <string name="special_deck_starry_sky_subtitle_1">Collected %1$d from %2$d!</string>
    <string name="special_deck_starry_sky_subtitle_2_1">Select any star</string>
    <string name="special_deck_starry_sky_subtitle_2_2">Your card has already been unveiled. Please return for a new one in %1$dh %2$dm.</string>
    <string name="special_deck_starry_star_press_me_tooltip">Tap the star</string>
    <string name="special_deck_starry_sky_button_layout">Prompt</string>
</resources>