package com.metacards.metacards.features.special_deck.domain.entity

import android.os.Parcelable
import androidx.core.net.toUri
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.deck.domain.entity.DeckId
import kotlinx.parcelize.Parcelize
import java.util.Date

@Parcelize
data class SpecialDeckCard(
    val id: CardId,
    val arObject: String,
    val connections: List<CardId>,
    val coordinates: SpecialDeckCardCoordinate,
    val imageUrl: String,
    val quoteLocalized: LocalizableString,
    val size: SpecialDeckCardSize,
    val texture: String
) : Parcelable {
    companion object {
        val mock: SpecialDeckCard = SpecialDeckCard(
            id = CardId(""),
            arObject = "",
            connections = listOf(),
            coordinates = SpecialDeckCardCoordinate(x = 0, y = 0),
            imageUrl = "",
            quoteLocalized = LocalizableString.createNonLocalizable(""),
            size = SpecialDeckCardSize.MINI,
            texture = ""
        )
    }

    fun toUserSpecialDeckCardInfo() = UserSpecialDeckCardInfo(id, Date())
}

@Parcelize
data class SpecialDeckCardCoordinate(
    val x: Int,
    val y: Int
) : Parcelable

enum class SpecialDeckCardSize {
    MINI, BIG;

    fun getDrawableRes() = if (this == MINI) R.drawable.ic_star_mini else R.drawable.ic_star_big

    companion object {
        fun fromString(value: String) = if (value == "MINI") MINI else BIG
    }
}

fun SpecialDeckCard.toCard(deckId: DeckId): Card {
    return Card(
        id = id,
        deckId = deckId,
        imageUrl = imageUrl,
        textureUri = texture.toUri(),
        arObjectUrl = arObject,
        gifUrl = null,
    )
}