digraph {
account
advbanner
analytics
auth
course
deck
deeplink
favorite_cards
home
layout
main
payments
record
root
showcase
special_deck
splash
tutorial
user
user_analytics
video_player
welcome
account -> advbanner
account -> analytics
account -> auth
account -> deck
account -> home
account -> layout
account -> special_deck
account -> tutorial
account -> user
account -> video_player
advbanner -> deck
advbanner -> home
advbanner -> user
auth -> analytics
auth -> user
course -> analytics
course -> deck
course -> home
course -> layout
course -> user
course -> video_player
deck -> analytics
deck -> payments
deck -> user
deeplink -> auth
deeplink -> course
deeplink -> deck
deeplink -> user
deeplink -> user_analytics
favorite_cards -> analytics
favorite_cards -> deck
favorite_cards -> record
favorite_cards -> user
home -> advbanner
home -> analytics
home -> deck
home -> special_deck
home -> tutorial
home -> user
layout -> analytics
layout -> course
layout -> deck
layout -> home
layout -> record
layout -> special_deck
layout -> tutorial
layout -> user
main -> account
main -> analytics
main -> course
main -> deck
main -> favorite_cards
main -> home
main -> layout
main -> record
main -> showcase
main -> tutorial
main -> user
main -> user_analytics
payments -> analytics
record -> analytics
record -> course
record -> deck
record -> home
record -> tutorial
record -> user
root -> account
root -> analytics
root -> auth
root -> course
root -> deck
root -> deeplink
root -> favorite_cards
root -> layout
root -> main
root -> payments
root -> record
root -> special_deck
root -> splash
root -> tutorial
root -> user
root -> user_analytics
root -> welcome
showcase -> account
showcase -> course
showcase -> deck
showcase -> favorite_cards
showcase -> home
showcase -> layout
showcase -> special_deck
showcase -> user
special_deck -> deck
special_deck -> user
tutorial -> analytics
user -> analytics
user -> payments
user_analytics -> analytics
user_analytics -> home
user_analytics -> record
user_analytics -> user
welcome -> analytics
}
