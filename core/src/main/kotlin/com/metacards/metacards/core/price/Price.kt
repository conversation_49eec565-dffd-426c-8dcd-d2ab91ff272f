package com.metacards.metacards.core.price

data class Price(
    val value: Double,
    val currency: Currency
) {
    constructor(value: Double) : this(value, Currency.Rub)

    override fun toString(): String {
        val value = "%.2f".format(value)
        return "$value $currency"
    }

    fun toDecimalString(): String {
        return "${value.toInt()} $currency"
    }
}

sealed class Currency(val symbol: String) {
    data object Rub : Currency("₽")
    class Other(symbol: String) : Currency(symbol)

    override fun toString(): String {
        return symbol
    }
}